@import './variables.scss';
@import './mixin.scss';
@import './transition.scss';
@import './element-ui.scss';
@import './sidebar.scss';
@import './btn.scss';
@import './common.scss';

body {
  height: 100%;
  overflow: hidden;
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: Microsoft YaHei, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Arial, sans-serif !important;
}

label {
  font-weight: 600 !important;
}


html {
  height: 100%;
  box-sizing: border-box;
  font-size: 18px;
}

#app {
  height: 100%;
}

*,
*:before,
*:after {
  box-sizing: inherit;
}

a:focus,
a:active {
  outline: none;
}

a,
a:focus,
a:hover {
  cursor: pointer;
  color: inherit;
  text-decoration: none;
}

div:focus {
  outline: none;
}

.fr {
  float: right;
}

.fl {
  float: left;
}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}

.icon-aside {
  background: #eef1f6;
  padding: 8px 24px;
  margin-bottom: 20px;
  border-radius: 2px;
  display: block;
  line-height: 32px;
  font-size: 16px;
  font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, Oxygen, Ubuntu, Cantarell, "Fira Sans", "Droid Sans", "Helvetica Neue", sans-serif;
  color: #2c3e50;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;

  a {
    color: #337ab7;
    cursor: pointer;

    &:hover {
      color: rgb(32, 160, 255);
    }
  }
}

.app-container {
  padding: 20px;
}

ul {
  padding: 0;
  margin: 0;
}


.preview {
  .vue-repl {
    border: none !important;
    box-shadow: none !important;

    .vertical .left {
      border: none !important;
    }

    .output-header {
      display: none !important;
    }
  }
}

::-webkit-scrollbar {
  width: 4px;
}

::-webkit-scrollbar:horizontal {
  height: 4px;
}



.kwt {
  max-width: calc(100vw - 215px);
  height: calc(100vh - 300px);
}

.techniqueScreenBox .tableBody .item.active {
  background: rgba(221, 227, 233, 0.4);
  border-radius: 8px;
}

.techniqueScreenBox .tableBody .item div {
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  font-size: 14px;
  height: 100%;
}

.techniqueScreenBox .tableHeader div {
  white-space: nowrap;
  font-size: 16px;
}

.techniqueScreenBox .emptyImage {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 30px;
  color: #fff;
}

.videoGuide {
  display: flex;
  justify-content: center;
  align-items: center;
  img {
    width: 35px;
    transform: scale(1.3);
    cursor: pointer;
  }
}

.picGuide {
  display: flex;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  img {
    width: 35px;
    cursor: pointer;
  }
}