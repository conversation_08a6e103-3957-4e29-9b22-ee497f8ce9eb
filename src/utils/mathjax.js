/**
 * MathJax工具类
 * 处理MathJax的加载、配置和渲染
 * 支持完全离线部署，不依赖CDN
 */

import { getMathJaxConfig, preloadMathJaxFonts, MATHJAX_CONFIG } from '@/config/mathjax.config.js'

class MathJaxUtil {
  constructor() {
    this.isReady = false
    this.isLoading = false
    this.loadPromise = null
    this.fontsPreloaded = false
  }

  /**
   * 初始化MathJax
   */
  async init() {
    if (this.isReady) {
      return Promise.resolve()
    }

    if (this.isLoading) {
      return this.loadPromise
    }

    // 预加载字体
    if (!this.fontsPreloaded) {
      preloadMathJaxFonts()
      this.fontsPreloaded = true
    }

    this.isLoading = true
    this.loadPromise = this._loadMathJax()

    try {
      await this.loadPromise
      this.isReady = true
      console.log('MathJax初始化完成')
    } catch (error) {
      console.error('MathJax初始化失败:', error)
      this.isLoading = false
      throw error
    }

    return this.loadPromise
  }

  /**
   * 加载MathJax
   */
  async _loadMathJax() {
    // 检查是否已经存在
    if (window.MathJax) {
      console.log('MathJax已存在')
      return Promise.resolve()
    }

    // 使用配置文件中的MathJax配置
    window.MathJax = getMathJaxConfig()

    // 尝试不同的加载方式
    return this._tryLoadMethods()
  }

  /**
   * 尝试不同的加载方法
   */
  async _tryLoadMethods() {
    // 优先使用本地资源，确保离线环境可用
    const methods = [
      () => this._loadFromLocalWithFont(),
      () => this._loadFromNodeModules(),
      () => this._loadFromCDN()
    ]

    for (let i = 0; i < methods.length; i++) {
      try {
        console.log(`尝试加载方法 ${i + 1}/${methods.length}`)
        await methods[i]()
        return
      } catch (error) {
        console.warn(`MathJax加载方法 ${i + 1} 失败:`, error.message)
        if (i === methods.length - 1) {
          throw new Error('所有MathJax加载方法都失败了')
        }
      }
    }
  }

  /**
   * 从本地静态资源加载（带字体支持）
   */
  _loadFromLocalWithFont() {
    return new Promise((resolve, reject) => {
      console.log('尝试从本地静态资源加载MathJax（带字体支持）')

      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.async = true
      script.src = MATHJAX_CONFIG.LOCAL_PATHS.WITH_FONT

      script.onload = () => {
        console.log('从本地加载MathJax（带字体）成功')
        resolve()
      }

      script.onerror = () => {
        reject(new Error('从本地加载MathJax（带字体）失败'))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * 从本地静态资源加载
   */
  _loadFromNodeModules() {
    return new Promise((resolve, reject) => {
      console.log('尝试从本地静态资源加载MathJax')

      // 尝试多个可能的路径
      const possiblePaths = [
        MATHJAX_CONFIG.LOCAL_PATHS.BASIC,
        '/node_modules/mathjax/tex-mml-chtml.js',
        './node_modules/mathjax/tex-mml-chtml.js'
      ]

      this._tryLoadFromPaths(possiblePaths, resolve, reject)
    })
  }

  /**
   * 尝试从多个路径加载
   */
  _tryLoadFromPaths(paths, resolve, reject, index = 0) {
    if (index >= paths.length) {
      reject(new Error('所有本地路径都加载失败'))
      return
    }

    const script = document.createElement('script')
    script.type = 'text/javascript'
    script.async = true
    script.src = paths[index]

    script.onload = () => {
      console.log(`从路径 ${paths[index]} 加载MathJax成功`)
      resolve()
    }

    script.onerror = () => {
      console.warn(`路径 ${paths[index]} 加载失败，尝试下一个`)
      this._tryLoadFromPaths(paths, resolve, reject, index + 1)
    }

    document.head.appendChild(script)
  }

  /**
   * 从CDN加载
   */
  _loadFromCDN() {
    return new Promise((resolve, reject) => {
      console.log('尝试从CDN加载MathJax')

      const script = document.createElement('script')
      script.type = 'text/javascript'
      script.async = true
      script.src = MATHJAX_CONFIG.CDN_PATHS.MATHJAX

      script.onload = () => {
        console.log('从CDN加载MathJax成功')
        resolve()
      }

      script.onerror = () => {
        reject(new Error('从CDN加载MathJax失败'))
      }

      document.head.appendChild(script)
    })
  }

  /**
   * 渲染指定元素中的数学公式
   */
  async render(element) {
    if (!this.isReady) {
      await this.init()
    }

    if (window.MathJax && window.MathJax.typesetPromise) {
      try {
        await window.MathJax.typesetPromise([element])
        console.log('MathJax渲染完成')
      } catch (error) {
        console.error('MathJax渲染错误:', error)
        throw error
      }
    } else {
      throw new Error('MathJax未正确加载')
    }
  }

  /**
   * 检测内容是否包含数学公式
   */
  containsMath(content) {
    if (!content) return false

    const mathPatterns = [
      /\$.*?\$/,           // 行内公式 $...$
      /\$\$.*?\$\$/,       // 显示公式 $$...$$
      /\\\(.*?\\\)/,       // 行内公式 \(...\)
      /\\\[.*?\\\]/,       // 显示公式 \[...\]
      /\\begin\{.*?\}.*?\\end\{.*?\}/,  // 环境公式
      /\\[a-zA-Z]+/        // LaTeX命令
    ]

    return mathPatterns.some(pattern => pattern.test(content))
  }

  /**
   * 获取MathJax状态
   */
  getStatus() {
    return {
      isReady: this.isReady,
      isLoading: this.isLoading,
      hasMathJax: !!window.MathJax
    }
  }
}

// 创建单例实例
const mathJaxUtil = new MathJaxUtil()

export default mathJaxUtil
